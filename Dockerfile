# Use OpenJDK 17 as the base image
FROM openjdk:17-jdk-slim

# Set working directory
WORKDIR /app

# Install necessary packages for building
RUN apt-get update && apt-get install -y \
    curl \
    dos2unix \
    && rm -rf /var/lib/apt/lists/*

# Copy Gradle wrapper and build configuration files first for better caching
COPY gradlew ./
COPY gradlew.bat ./
COPY gradle/ gradle/
COPY build.gradle.kts settings.gradle.kts gradle.properties ./

# Fix line endings and make gradlew executable
RUN dos2unix gradlew && chmod +x gradlew

# Download dependencies (this layer will be cached if dependencies don't change)
RUN ./gradlew dependencies --no-daemon

# Copy source code
COPY src/ src/

# Build the application
RUN ./gradlew buildFatJar --no-daemon

# Copy the built jar to the working directory
RUN cp build/libs/coffee-server-fat.jar ./coffee-server.jar

# Expose the port the app runs on
EXPOSE 8080

# Set environment variables
ENV JAVA_OPTS="-Xmx512m -Xms256m"

# Run the application
CMD ["sh", "-c", "java $JAVA_OPTS -jar coffee-server.jar"]
