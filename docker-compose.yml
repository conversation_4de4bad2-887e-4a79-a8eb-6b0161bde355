version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: coffee-postgres
    environment:
      POSTGRES_DB: coffee_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - coffee-network

  coffee-server:
    build: .
    container_name: coffee-server
    environment:
      USE_POSTGRES: "true"
      POSTGRES_PASSWORD: "postgres"
    ports:
      - "8080:8080"
    networks:
      - coffee-network

networks:
  coffee-network:
    driver: bridge

volumes:
  postgres_data:
