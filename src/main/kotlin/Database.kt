package com.gulderbone

import models.data.Items
import models.data.Orders
import models.data.OrderItems
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.transactions.transaction

fun configureDatabase() {
    val databaseUrl = "******************************************"
    val driver = "org.postgresql.Driver"
    val user = "postgres"
    val password = "postgres"

    Database.connect(
        url = databaseUrl,
        driver = driver,
        user = user,
        password = password
    )

    transaction {
        SchemaUtils.create(Items, Orders, OrderItems)
    }
}
