package com.gulderbone

import models.data.Items
import models.data.Orders
import models.data.OrderItems
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.transactions.transaction

fun configureDatabase() {
    // Check for DATABASE_URL first (common in cloud deployments)
    val databaseUrl = System.getenv("DATABASE_URL")

    if (databaseUrl != null) {
        // Parse DATABASE_URL (format: postgresql://user:password@host:port/database)
        val uri = java.net.URI(databaseUrl)
        val host = uri.host
        val port = uri.port
        val database = uri.path.substring(1) // Remove leading slash
        val userInfo = uri.userInfo?.split(":")
        val user = userInfo?.get(0) ?: "postgres"
        val password = userInfo?.get(1) ?: "postgres"

        val jdbcUrl = "***************************************"

        Database.connect(
            url = jdbcUrl,
            driver = "org.postgresql.Driver",
            user = user,
            password = password
        )
    } else {
        // Fall back to individual environment variables
        val dbHost = System.getenv("DB_HOST") ?: "localhost"
        val dbPort = System.getenv("DB_PORT") ?: "5432"
        val dbName = System.getenv("DB_NAME") ?: "coffee_db"
        val dbUser = System.getenv("DB_USER") ?: "postgres"
        val dbPassword = System.getenv("DB_PASSWORD") ?: "postgres"

        val jdbcUrl = "*****************************************"

        Database.connect(
            url = jdbcUrl,
            driver = "org.postgresql.Driver",
            user = dbUser,
            password = dbPassword
        )
    }

    transaction {
        SchemaUtils.create(Items, Orders, OrderItems)
    }
}
